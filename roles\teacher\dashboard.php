<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('teacher');

$pageTitle = 'Teacher Dashboard';

// Get teacher's statistics
$pdo = getDBConnection();
$teacherId = $_SESSION['user_id'];

// Get teacher's exams
$myExams = getExamsByTeacher($teacherId);

// Count exams by status
$examStats = [
    'total' => count($myExams),
    'active' => 0,
    'draft' => 0,
    'completed' => 0
];

foreach ($myExams as $exam) {
    $examStats[$exam['status']]++;
}

// Get teacher's subjects
$stmt = $pdo->prepare("SELECT s.*, COUNT(e.id) as exam_count 
                      FROM subjects s 
                      LEFT JOIN exams e ON s.id = e.subject_id 
                      WHERE s.teacher_id = ? AND s.is_active = 1 
                      GROUP BY s.id");
$stmt->execute([$teacherId]);
$mySubjects = $stmt->fetchAll();

// Get recent exam attempts for teacher's exams
$stmt = $pdo->prepare("SELECT ea.*, e.title as exam_title, u.first_name, u.last_name, u.student_id
                      FROM exam_attempts ea 
                      JOIN exams e ON ea.exam_id = e.id 
                      JOIN users u ON ea.student_id = u.id 
                      WHERE e.teacher_id = ? 
                      ORDER BY ea.created_at DESC 
                      LIMIT 10");
$stmt->execute([$teacherId]);
$recentAttempts = $stmt->fetchAll();

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chalkboard-teacher me-2"></i>Teacher Dashboard</h2>
    <div>
        <span class="text-muted">Welcome back, <?php echo $_SESSION['first_name']; ?>!</span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $examStats['total']; ?></h3>
                <p class="mb-0">Total Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-play-circle fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $examStats['active']; ?></h3>
                <p class="mb-0">Active Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-edit fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $examStats['draft']; ?></h3>
                <p class="mb-0">Draft Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-book fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo count($mySubjects); ?></h3>
                <p class="mb-0">My Subjects</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-rocket me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="create_exam.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create New Exam
                    </a>
                    <a href="question_bank.php" class="btn btn-info">
                        <i class="fas fa-question me-2"></i>Manage Question Bank
                    </a>
                    <a href="evaluate_exam.php" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Evaluate Exams
                    </a>
                    <a href="my_exams.php" class="btn btn-secondary">
                        <i class="fas fa-list me-2"></i>View All My Exams
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-book me-2"></i>My Subjects</h5>
            </div>
            <div class="card-body">
                <?php if (empty($mySubjects)): ?>
                    <p class="text-muted text-center">No subjects assigned yet.</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($mySubjects as $subject): ?>
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong><?php echo htmlspecialchars($subject['name']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($subject['code']); ?></small>
                            </div>
                            <span class="badge bg-primary rounded-pill"><?php echo $subject['exam_count']; ?> exams</span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Exams -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clipboard-list me-2"></i>Recent Exams</h5>
                <a href="my_exams.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($myExams)): ?>
                    <p class="text-muted text-center">No exams created yet. <a href="create_exam.php">Create your first exam</a>.</p>
                <?php else: ?>
                    <div class="row">
                        <?php foreach (array_slice($myExams, 0, 3) as $exam): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card exam-card">
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($exam['title']); ?></h6>
                                    <p class="card-text">
                                        <small class="text-muted"><?php echo htmlspecialchars($exam['subject_name']); ?></small>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="exam-status status-<?php echo $exam['status']; ?>">
                                            <?php echo ucfirst($exam['status']); ?>
                                        </span>
                                        <small class="text-muted"><?php echo formatDate($exam['start_time']); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Student Attempts -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-users me-2"></i>Recent Student Attempts</h5>
                <a href="evaluate_exam.php" class="btn btn-sm btn-outline-primary">Evaluate All</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentAttempts)): ?>
                    <p class="text-muted text-center">No student attempts yet.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Student</th>
                                    <th>Exam</th>
                                    <th>Status</th>
                                    <th>Score</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentAttempts as $attempt): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($attempt['first_name'] . ' ' . $attempt['last_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($attempt['student_id']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($attempt['exam_title']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $attempt['status'] == 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($attempt['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($attempt['status'] == 'completed'): ?>
                                            <strong><?php echo number_format($attempt['percentage'], 1); ?>%</strong>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo formatDate($attempt['created_at']); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../../templates/footer.php'; ?>
