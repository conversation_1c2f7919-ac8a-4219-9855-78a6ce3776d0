# WBEMS - Web-Based Exam Management System

A comprehensive online exam management system built with PHP, MySQL, HTML, CSS, and JavaScript. Designed for educational institutions to streamline exam creation, participation, evaluation, and reporting with role-based access control.

## 🚀 Features

### Role-Based Access Control
- **Admin**: Manage users, assign roles, view logs, manage system settings
- **Teacher**: Create/manage exams, questions, view student submissions, grade exams
- **Student**: Take exams, view results, review past exams
- **Department Head**: Monitor all exams in the department, view teacher activities
- **Registrar**: Generate system-wide exam reports, export data

### Core Functionality
- ✅ User authentication and session management
- ✅ Role-based dashboard and navigation
- ✅ Exam creation with multiple question types
- ✅ Real-time exam taking interface
- ✅ Automatic and manual grading
- ✅ Comprehensive reporting and analytics
- ✅ Activity logging and monitoring
- ✅ Responsive design for all devices

## 🛠️ Technologies Used

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.1.3
- **Icons**: Font Awesome 6.0
- **Server**: Apache (XAMPP)

## 📁 Project Structure

```
WBEMS/
│
├── assets/                 
│   ├── css/                # Custom styles
│   ├── js/                 # JavaScript scripts
│   └── images/             # Images/logos
│
├── config/                 
│   ├── db.php              # Database connection
│   └── constants.php       # System constants
│
├── includes/              
│   ├── auth.php            # Session & login verification
│   └── functions.php       # Shared functions
│
├── roles/                 
│   ├── admin/              # Admin functionality
│   ├── teacher/            # Teacher functionality
│   ├── student/            # Student functionality
│   ├── department_head/    # Department Head functionality
│   └── registrar/          # Registrar functionality
│
├── templates/             
│   ├── header.php          # Common header
│   ├── footer.php          # Common footer
│   └── sidebar.php         # Role-based sidebar
│
├── database/
│   └── schema.sql          # Database structure
│
├── login.php              # User login
├── logout.php             # User logout
├── dashboard.php          # Role-based redirection
├── index.php              # Welcome page
├── install.php            # System installation
└── README.md              # This file
```

## 🔧 Installation

### Prerequisites
- XAMPP with Apache and MySQL running
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser (Chrome, Firefox, Safari, Edge)

### Step-by-Step Installation

1. **Download and Setup XAMPP**
   - Download XAMPP from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install and start Apache and MySQL services

2. **Clone/Download the Project**
   ```bash
   # Place the project in XAMPP's htdocs folder
   C:\xampp\htdocs\WBEMS\
   ```

3. **Database Configuration**
   - Open phpMyAdmin: `http://localhost/phpmyadmin`
   - The installation script will create the database automatically
   - Default settings in `config/db.php`:
     - Host: localhost
     - Username: root
     - Password: (empty)
     - Database: wbems_db

4. **Run Installation**
   - Open your browser and navigate to: `http://localhost/WBEMS/install.php`
   - Click "Install WBEMS" to create the database and sample data
   - Follow the on-screen instructions

5. **Access the System**
   - Navigate to: `http://localhost/WBEMS/`
   - Use the demo accounts to login:

## 🔐 Demo Accounts

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Admin | admin | password | System administrator |
| Teacher | teacher1 | password | Sample teacher account |
| Student | student1 | password | Sample student account |

## 🎯 User Roles & Permissions

### Admin
- Manage all users (create, edit, delete)
- View system activity logs
- Manage departments and subjects
- System configuration and settings

### Teacher
- Create and manage exams
- Build question banks
- Evaluate student submissions
- View student performance reports

### Student
- Take available exams
- View exam results and history
- Access enrolled subjects
- Review past exam attempts

### Department Head
- Monitor all exams in department
- View teacher activities and performance
- Generate department reports
- Oversee department operations

### Registrar
- Generate system-wide reports
- Export data for analysis
- View comprehensive analytics
- Monitor overall system performance

## 🔧 Configuration

### Database Settings
Edit `config/db.php` to modify database connection:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'wbems_db');
```

### System Constants
Modify `config/constants.php` for system settings:
```php
define('SITE_NAME', 'WBEMS - Web-Based Exam Management System');
define('SITE_URL', 'http://localhost/WBEMS/');
define('SESSION_TIMEOUT', 3600); // 1 hour
```

## 🚀 Usage

### For Teachers
1. Login with teacher credentials
2. Navigate to "Create Exam" to build new exams
3. Use "Question Bank" to manage reusable questions
4. Monitor student attempts in "Evaluate Exams"

### For Students
1. Login with student credentials
2. View available exams in "Take Exam"
3. Complete exams within the time limit
4. Check results in "View Results"

### For Administrators
1. Login with admin credentials
2. Manage users through "Manage Users"
3. Monitor system activity in "View Logs"
4. Configure departments and subjects

## 🛡️ Security Features

- Password hashing using PHP's `password_hash()`
- Session-based authentication
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- Role-based access control
- Activity logging for audit trails

## 🔄 Database Schema

The system uses a normalized database structure with the following main tables:
- `users` - All system users with role-based access
- `departments` - Academic departments
- `subjects` - Courses/subjects
- `exams` - Exam definitions
- `questions` - Exam questions
- `question_options` - Multiple choice options
- `exam_attempts` - Student exam attempts
- `student_answers` - Individual question responses
- `enrollments` - Student-subject relationships
- `activity_logs` - System activity tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🆘 Support

For support and questions:
- Check the documentation
- Review the code comments
- Create an issue on GitHub

## 🔮 Future Enhancements

- Email notifications for exam schedules
- Advanced question types (drag-drop, matching)
- Plagiarism detection
- Mobile app development
- Integration with LMS platforms
- Advanced analytics and AI insights

---

**WBEMS** - Streamlining education through technology 🎓
