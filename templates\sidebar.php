<div class="sidebar">
    <div class="p-3">
        <h6 class="text-muted mb-3">Navigation</h6>
        <ul class="nav flex-column">
            <?php
            $role = $_SESSION['role'];
            $currentPage = basename($_SERVER['PHP_SELF']);
            
            // Dashboard link for all roles
            echo '<li class="nav-item">
                    <a class="nav-link ' . ($currentPage == 'dashboard.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/' . $role . '/dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                  </li>';
            
            // Role-specific menu items
            switch($role) {
                case 'admin':
                    echo '<li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'manage_users.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/admin/manage_users.php">
                                <i class="fas fa-users me-2"></i>Manage Users
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'view_logs.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/admin/view_logs.php">
                                <i class="fas fa-list me-2"></i>View Logs
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/admin/manage_departments.php">
                                <i class="fas fa-building me-2"></i>Departments
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/admin/manage_subjects.php">
                                <i class="fas fa-book me-2"></i>Subjects
                            </a>
                          </li>';
                    break;
                    
                case 'teacher':
                    echo '<li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'create_exam.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/teacher/create_exam.php">
                                <i class="fas fa-plus me-2"></i>Create Exam
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'question_bank.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/teacher/question_bank.php">
                                <i class="fas fa-question me-2"></i>Question Bank
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'evaluate_exam.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/teacher/evaluate_exam.php">
                                <i class="fas fa-check me-2"></i>Evaluate Exams
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/teacher/my_exams.php">
                                <i class="fas fa-clipboard-list me-2"></i>My Exams
                            </a>
                          </li>';
                    break;
                    
                case 'student':
                    echo '<li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'take_exam.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/student/take_exam.php">
                                <i class="fas fa-pencil-alt me-2"></i>Take Exam
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'view_results.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/student/view_results.php">
                                <i class="fas fa-chart-line me-2"></i>View Results
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'exam_history.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/student/exam_history.php">
                                <i class="fas fa-history me-2"></i>Exam History
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/student/my_subjects.php">
                                <i class="fas fa-book-open me-2"></i>My Subjects
                            </a>
                          </li>';
                    break;
                    
                case 'department_head':
                    echo '<li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'monitor_exams.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/department_head/monitor_exams.php">
                                <i class="fas fa-monitor me-2"></i>Monitor Exams
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/department_head/department_reports.php">
                                <i class="fas fa-chart-bar me-2"></i>Department Reports
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/department_head/teacher_activities.php">
                                <i class="fas fa-user-tie me-2"></i>Teacher Activities
                            </a>
                          </li>';
                    break;
                    
                case 'registrar':
                    echo '<li class="nav-item">
                            <a class="nav-link ' . ($currentPage == 'generate_reports.php' ? 'active' : '') . '" href="' . SITE_URL . 'roles/registrar/generate_reports.php">
                                <i class="fas fa-file-alt me-2"></i>Generate Reports
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/registrar/system_reports.php">
                                <i class="fas fa-chart-pie me-2"></i>System Reports
                            </a>
                          </li>
                          <li class="nav-item">
                            <a class="nav-link" href="' . SITE_URL . 'roles/registrar/export_data.php">
                                <i class="fas fa-download me-2"></i>Export Data
                            </a>
                          </li>';
                    break;
            }
            ?>
        </ul>
    </div>
</div>
