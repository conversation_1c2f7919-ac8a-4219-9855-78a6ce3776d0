<?php
// Test database connection for WBEMS
require_once 'config/db.php';

echo "<h2>WBEMS Database Connection Test</h2>";

try {
    // Test basic MySQL connection
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ MySQL connection successful</p>";
    
    // Check if database exists
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([DB_NAME]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Database '" . DB_NAME . "' exists</p>";
        
        // Test connection to specific database
        $pdo = getDBConnection();
        echo "<p style='color: green;'>✓ Database connection successful</p>";
        
        // Check if tables exist
        $stmt = $pdo->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ Found " . count($tables) . " tables in database</p>";
            echo "<p>Tables: " . implode(', ', $tables) . "</p>";
            
            // Check if admin user exists
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
            $stmt->execute();
            $adminExists = $stmt->fetch()['count'] > 0;
            
            if ($adminExists) {
                echo "<p style='color: green;'>✓ Admin user exists</p>";
                echo "<p><strong>System is ready!</strong> You can <a href='login.php'>login here</a></p>";
            } else {
                echo "<p style='color: orange;'>⚠ Admin user not found. Please run the installer.</p>";
                echo "<p><a href='install.php'>Run Installation</a></p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Database is empty. Please run the installer.</p>";
            echo "<p><a href='install.php'>Run Installation</a></p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Database '" . DB_NAME . "' does not exist. Please run the installer.</p>";
        echo "<p><a href='install.php'>Run Installation</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/db.php</p>";
    echo "<p>Make sure XAMPP MySQL service is running.</p>";
}

echo "<hr>";
echo "<h3>System Information</h3>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Database Host:</strong> " . DB_HOST . "</p>";
echo "<p><strong>Database Name:</strong> " . DB_NAME . "</p>";
echo "<p><strong>Database User:</strong> " . DB_USER . "</p>";

// Check PHP extensions
$extensions = ['pdo', 'pdo_mysql', 'mysqli'];
echo "<h4>Required PHP Extensions:</h4>";
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext (missing)</p>";
    }
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Home</a> | <a href='install.php'>Run Installation</a></p>";
?>
