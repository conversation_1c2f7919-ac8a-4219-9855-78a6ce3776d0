<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('student');

$pageTitle = 'Student Dashboard';

// Get student's statistics
$pdo = getDBConnection();
$studentId = $_SESSION['user_id'];

// Get available exams for student
$availableExams = getAvailableExamsForStudent($studentId);

// Get student's exam attempts
$stmt = $pdo->prepare("SELECT ea.*, e.title as exam_title, s.name as subject_name 
                      FROM exam_attempts ea 
                      JOIN exams e ON ea.exam_id = e.id 
                      JOIN subjects s ON e.subject_id = s.id 
                      WHERE ea.student_id = ? 
                      ORDER BY ea.created_at DESC");
$stmt->execute([$studentId]);
$myAttempts = $stmt->fetchAll();

// Calculate statistics
$stats = [
    'total_attempts' => count($myAttempts),
    'completed' => 0,
    'in_progress' => 0,
    'average_score' => 0
];

$totalScore = 0;
$completedCount = 0;

foreach ($myAttempts as $attempt) {
    if ($attempt['status'] == 'completed') {
        $stats['completed']++;
        $totalScore += $attempt['percentage'];
        $completedCount++;
    } elseif ($attempt['status'] == 'in_progress') {
        $stats['in_progress']++;
    }
}

if ($completedCount > 0) {
    $stats['average_score'] = $totalScore / $completedCount;
}

// Get student's subjects
$stmt = $pdo->prepare("SELECT s.*, u.first_name, u.last_name 
                      FROM subjects s 
                      JOIN enrollments en ON s.id = en.subject_id 
                      LEFT JOIN users u ON s.teacher_id = u.id 
                      WHERE en.student_id = ? AND en.status = 'active'");
$stmt->execute([$studentId]);
$mySubjects = $stmt->fetchAll();

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-user-graduate me-2"></i>Student Dashboard</h2>
    <div>
        <span class="text-muted">Welcome back, <?php echo $_SESSION['first_name']; ?>!</span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo count($availableExams); ?></h3>
                <p class="mb-0">Available Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $stats['completed']; ?></h3>
                <p class="mb-0">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $stats['in_progress']; ?></h3>
                <p class="mb-0">In Progress</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo number_format($stats['average_score'], 1); ?>%</h3>
                <p class="mb-0">Average Score</p>
            </div>
        </div>
    </div>
</div>

<!-- Available Exams -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-play-circle me-2"></i>Available Exams</h5>
                <a href="take_exam.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($availableExams)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No exams available at the moment.</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach (array_slice($availableExams, 0, 3) as $exam): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card exam-card">
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($exam['title']); ?></h6>
                                    <p class="card-text">
                                        <small class="text-muted"><?php echo htmlspecialchars($exam['subject_name']); ?></small>
                                    </p>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?php echo $exam['duration_minutes']; ?> minutes
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i><?php echo formatDate($exam['start_time']); ?>
                                        </small>
                                    </div>
                                    <?php if ($exam['attempt_count'] > 0): ?>
                                        <span class="badge bg-warning">Already Attempted</span>
                                    <?php else: ?>
                                        <a href="take_exam.php?id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-play me-1"></i>Start Exam
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Subjects -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-rocket me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="take_exam.php" class="btn btn-primary">
                        <i class="fas fa-pencil-alt me-2"></i>Take Exam
                    </a>
                    <a href="view_results.php" class="btn btn-info">
                        <i class="fas fa-chart-line me-2"></i>View Results
                    </a>
                    <a href="exam_history.php" class="btn btn-success">
                        <i class="fas fa-history me-2"></i>Exam History
                    </a>
                    <a href="my_subjects.php" class="btn btn-secondary">
                        <i class="fas fa-book-open me-2"></i>My Subjects
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-book-open me-2"></i>My Subjects</h5>
            </div>
            <div class="card-body">
                <?php if (empty($mySubjects)): ?>
                    <p class="text-muted text-center">No subjects enrolled yet.</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($mySubjects as $subject): ?>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong><?php echo htmlspecialchars($subject['name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($subject['code']); ?></small>
                                    <?php if ($subject['first_name']): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($subject['first_name'] . ' ' . $subject['last_name']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                                <span class="badge bg-primary"><?php echo $subject['credits']; ?> credits</span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Exam Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-chart-bar me-2"></i>Recent Exam Results</h5>
                <a href="view_results.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($myAttempts)): ?>
                    <p class="text-muted text-center">No exam attempts yet.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Exam</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Score</th>
                                    <th>Grade</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($myAttempts, 0, 5) as $attempt): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($attempt['exam_title']); ?></td>
                                    <td><?php echo htmlspecialchars($attempt['subject_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $attempt['status'] == 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($attempt['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($attempt['status'] == 'completed'): ?>
                                            <strong><?php echo number_format($attempt['percentage'], 1); ?>%</strong>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($attempt['status'] == 'completed'): ?>
                                            <span class="badge bg-<?php echo $attempt['percentage'] >= PASSING_GRADE ? 'success' : 'danger'; ?>">
                                                <?php echo calculateGradeLetter($attempt['percentage']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo formatDate($attempt['created_at']); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../../templates/footer.php'; ?>
