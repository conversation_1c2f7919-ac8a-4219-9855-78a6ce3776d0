<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('teacher');

$pageTitle = 'Create Exam';
$message = '';
$error = '';

$teacherId = $_SESSION['user_id'];

// Get teacher's subjects
$pdo = getDBConnection();
$stmt = $pdo->prepare("SELECT * FROM subjects WHERE teacher_id = ? AND is_active = 1 ORDER BY name");
$stmt->execute([$teacherId]);
$subjects = $stmt->fetchAll();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $title = sanitizeInput($_POST['title']);
        $description = sanitizeInput($_POST['description']);
        $subjectId = $_POST['subject_id'];
        $examType = $_POST['exam_type'];
        $totalMarks = (int)$_POST['total_marks'];
        $duration = (int)$_POST['duration_minutes'];
        $startTime = $_POST['start_time'];
        $endTime = $_POST['end_time'];
        $instructions = sanitizeInput($_POST['instructions']);
        $shuffleQuestions = isset($_POST['shuffle_questions']) ? 1 : 0;
        $showResults = isset($_POST['show_results']) ? 1 : 0;
        $allowReview = isset($_POST['allow_review']) ? 1 : 0;
        
        // Validation
        if (empty($title) || empty($subjectId) || empty($startTime) || empty($endTime)) {
            throw new Exception('Please fill in all required fields.');
        }
        
        if ($duration < MIN_EXAM_DURATION || $duration > MAX_EXAM_DURATION) {
            throw new Exception('Exam duration must be between ' . MIN_EXAM_DURATION . ' and ' . MAX_EXAM_DURATION . ' minutes.');
        }
        
        if (strtotime($startTime) >= strtotime($endTime)) {
            throw new Exception('End time must be after start time.');
        }
        
        // Insert exam
        $stmt = $pdo->prepare("INSERT INTO exams (title, description, subject_id, teacher_id, exam_type, total_marks, duration_minutes, start_time, end_time, instructions, shuffle_questions, show_results, allow_review) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $stmt->execute([
            $title,
            $description,
            $subjectId,
            $teacherId,
            $examType,
            $totalMarks,
            $duration,
            $startTime,
            $endTime,
            $instructions,
            $shuffleQuestions,
            $showResults,
            $allowReview
        ]);
        
        $examId = $pdo->lastInsertId();
        
        logActivity($teacherId, 'create_exam', 'Created exam: ' . $title);
        
        $message = 'Exam created successfully! You can now add questions to your exam.';
        
        // Redirect to question management
        header("Location: manage_questions.php?exam_id=$examId");
        exit();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-plus me-2"></i>Create New Exam</h2>
    <a href="dashboard.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
    </a>
</div>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (empty($subjects)): ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        You don't have any subjects assigned yet. Please contact the administrator to assign subjects to you.
    </div>
<?php else: ?>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-clipboard-list me-2"></i>Exam Details</h5>
    </div>
    <div class="card-body">
        <form method="POST" class="needs-validation" novalidate>
            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label for="title" class="form-label">Exam Title *</label>
                        <input type="text" class="form-control" name="title" id="title" required
                               value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>">
                        <div class="invalid-feedback">Please provide an exam title.</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="subject_id" class="form-label">Subject *</label>
                        <select class="form-select" name="subject_id" id="subject_id" required>
                            <option value="">Select Subject</option>
                            <?php foreach ($subjects as $subject): ?>
                            <option value="<?php echo $subject['id']; ?>" 
                                    <?php echo (isset($_POST['subject_id']) && $_POST['subject_id'] == $subject['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($subject['name'] . ' (' . $subject['code'] . ')'); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Please select a subject.</div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" name="description" id="description" rows="3"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
            </div>
            
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="exam_type" class="form-label">Exam Type *</label>
                        <select class="form-select" name="exam_type" id="exam_type" required>
                            <option value="quiz" <?php echo (isset($_POST['exam_type']) && $_POST['exam_type'] == 'quiz') ? 'selected' : ''; ?>>Quiz</option>
                            <option value="midterm" <?php echo (isset($_POST['exam_type']) && $_POST['exam_type'] == 'midterm') ? 'selected' : ''; ?>>Midterm</option>
                            <option value="final" <?php echo (isset($_POST['exam_type']) && $_POST['exam_type'] == 'final') ? 'selected' : ''; ?>>Final</option>
                            <option value="assignment" <?php echo (isset($_POST['exam_type']) && $_POST['exam_type'] == 'assignment') ? 'selected' : ''; ?>>Assignment</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="total_marks" class="form-label">Total Marks *</label>
                        <input type="number" class="form-control" name="total_marks" id="total_marks" min="1" max="1000" required
                               value="<?php echo isset($_POST['total_marks']) ? $_POST['total_marks'] : '100'; ?>">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="duration_minutes" class="form-label">Duration (minutes) *</label>
                        <input type="number" class="form-control" name="duration_minutes" id="duration_minutes" 
                               min="<?php echo MIN_EXAM_DURATION; ?>" max="<?php echo MAX_EXAM_DURATION; ?>" required
                               value="<?php echo isset($_POST['duration_minutes']) ? $_POST['duration_minutes'] : DEFAULT_EXAM_DURATION; ?>">
                        <div class="form-text">Between <?php echo MIN_EXAM_DURATION; ?> and <?php echo MAX_EXAM_DURATION; ?> minutes</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="start_time" class="form-label">Start Date & Time *</label>
                        <input type="datetime-local" class="form-control" name="start_time" id="start_time" required
                               value="<?php echo isset($_POST['start_time']) ? $_POST['start_time'] : ''; ?>">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="end_time" class="form-label">End Date & Time *</label>
                        <input type="datetime-local" class="form-control" name="end_time" id="end_time" required
                               value="<?php echo isset($_POST['end_time']) ? $_POST['end_time'] : ''; ?>">
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="instructions" class="form-label">Instructions for Students</label>
                <textarea class="form-control" name="instructions" id="instructions" rows="4" 
                          placeholder="Enter any special instructions for students taking this exam..."><?php echo isset($_POST['instructions']) ? htmlspecialchars($_POST['instructions']) : ''; ?></textarea>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="shuffle_questions" id="shuffle_questions" 
                               <?php echo (isset($_POST['shuffle_questions'])) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="shuffle_questions">
                            Shuffle Questions
                        </label>
                        <div class="form-text">Randomize question order for each student</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="show_results" id="show_results" checked
                               <?php echo (isset($_POST['show_results']) || !isset($_POST['show_results'])) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="show_results">
                            Show Results to Students
                        </label>
                        <div class="form-text">Allow students to see their scores</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" name="allow_review" id="allow_review" checked
                               <?php echo (isset($_POST['allow_review']) || !isset($_POST['allow_review'])) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="allow_review">
                            Allow Review
                        </label>
                        <div class="form-text">Let students review their answers</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Create Exam
                </button>
            </div>
        </form>
    </div>
</div>

<?php endif; ?>

<script>
// Set default start time to current time + 1 hour
document.addEventListener('DOMContentLoaded', function() {
    const startTimeInput = document.getElementById('start_time');
    const endTimeInput = document.getElementById('end_time');
    
    if (!startTimeInput.value) {
        const now = new Date();
        now.setHours(now.getHours() + 1);
        startTimeInput.value = now.toISOString().slice(0, 16);
    }
    
    // Auto-calculate end time based on duration
    function updateEndTime() {
        const startTime = new Date(startTimeInput.value);
        const duration = parseInt(document.getElementById('duration_minutes').value) || 60;
        
        if (startTime && duration) {
            const endTime = new Date(startTime.getTime() + duration * 60000);
            endTimeInput.value = endTime.toISOString().slice(0, 16);
        }
    }
    
    startTimeInput.addEventListener('change', updateEndTime);
    document.getElementById('duration_minutes').addEventListener('change', updateEndTime);
    
    // Initial calculation
    updateEndTime();
});
</script>

<?php include '../../templates/footer.php'; ?>
