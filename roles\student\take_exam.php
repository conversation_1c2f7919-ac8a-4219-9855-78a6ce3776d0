<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('student');

$pageTitle = 'Take Exam';
$studentId = $_SESSION['user_id'];

// Get available exams for student
$availableExams = getAvailableExamsForStudent($studentId);

// If exam ID is provided, show exam details
$selectedExam = null;
if (isset($_GET['id'])) {
    $examId = (int)$_GET['id'];
    
    // Get exam details
    $pdo = getDBConnection();
    $stmt = $pdo->prepare("SELECT e.*, s.name as subject_name, u.first_name, u.last_name 
                          FROM exams e 
                          JOIN subjects s ON e.subject_id = s.id 
                          JOIN users u ON e.teacher_id = u.id 
                          WHERE e.id = ? AND e.status = 'active'");
    $stmt->execute([$examId]);
    $selectedExam = $stmt->fetch();
    
    // Check if student is enrolled in the subject
    if ($selectedExam) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM enrollments 
                              WHERE student_id = ? AND subject_id = ? AND status = 'active'");
        $stmt->execute([$studentId, $selectedExam['subject_id']]);
        $isEnrolled = $stmt->fetch()['count'] > 0;
        
        if (!$isEnrolled) {
            $selectedExam = null;
            $error = "You are not enrolled in this subject.";
        }
    }
    
    // Check if student has already attempted this exam
    if ($selectedExam) {
        $stmt = $pdo->prepare("SELECT * FROM exam_attempts WHERE exam_id = ? AND student_id = ?");
        $stmt->execute([$examId, $studentId]);
        $existingAttempt = $stmt->fetch();
        
        if ($existingAttempt) {
            $error = "You have already attempted this exam.";
            $selectedExam = null;
        }
    }
    
    // Check if exam is currently active
    if ($selectedExam && !isExamActive($selectedExam)) {
        $error = "This exam is not currently available.";
        $selectedExam = null;
    }
}

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-pencil-alt me-2"></i>Take Exam</h2>
    <a href="dashboard.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
    </a>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if ($selectedExam): ?>
    <!-- Exam Details -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-info-circle me-2"></i>Exam Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h4><?php echo htmlspecialchars($selectedExam['title']); ?></h4>
                    <p class="text-muted mb-2">
                        <strong>Subject:</strong> <?php echo htmlspecialchars($selectedExam['subject_name']); ?>
                    </p>
                    <p class="text-muted mb-2">
                        <strong>Teacher:</strong> <?php echo htmlspecialchars($selectedExam['first_name'] . ' ' . $selectedExam['last_name']); ?>
                    </p>
                    <?php if ($selectedExam['description']): ?>
                        <p class="mb-2"><?php echo htmlspecialchars($selectedExam['description']); ?></p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6>Exam Details</h6>
                            <p class="mb-1"><strong>Duration:</strong> <?php echo $selectedExam['duration_minutes']; ?> minutes</p>
                            <p class="mb-1"><strong>Total Marks:</strong> <?php echo $selectedExam['total_marks']; ?></p>
                            <p class="mb-1"><strong>Type:</strong> <?php echo ucfirst($selectedExam['exam_type']); ?></p>
                            <p class="mb-0"><strong>Available Until:</strong> <?php echo formatDate($selectedExam['end_time']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if ($selectedExam['instructions']): ?>
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>Instructions</h6>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($selectedExam['instructions'])); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="alert alert-warning mt-3">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
                <ul class="mb-0">
                    <li>Once you start the exam, the timer will begin and cannot be paused.</li>
                    <li>Make sure you have a stable internet connection.</li>
                    <li>Do not refresh the page or navigate away during the exam.</li>
                    <li>Your answers will be automatically saved as you progress.</li>
                    <li>Submit your exam before the time runs out.</li>
                </ul>
            </div>
            
            <div class="text-center mt-4">
                <button type="button" class="btn btn-success btn-lg" onclick="startExam(<?php echo $selectedExam['id']; ?>)">
                    <i class="fas fa-play me-2"></i>Start Exam
                </button>
            </div>
        </div>
    </div>

<?php else: ?>
    <!-- Available Exams List -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list me-2"></i>Available Exams</h5>
        </div>
        <div class="card-body">
            <?php if (empty($availableExams)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">No Exams Available</h5>
                    <p class="text-muted">There are no active exams available for you at the moment.</p>
                    <a href="dashboard.php" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($availableExams as $exam): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card exam-card h-100">
                            <div class="card-body">
                                <h6 class="card-title"><?php echo htmlspecialchars($exam['title']); ?></h6>
                                <p class="card-text">
                                    <small class="text-muted"><?php echo htmlspecialchars($exam['subject_name']); ?></small>
                                </p>
                                
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between mb-1">
                                        <small><i class="fas fa-clock me-1"></i>Duration:</small>
                                        <small><?php echo $exam['duration_minutes']; ?> min</small>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <small><i class="fas fa-star me-1"></i>Marks:</small>
                                        <small><?php echo $exam['total_marks']; ?></small>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <small><i class="fas fa-calendar me-1"></i>Available Until:</small>
                                        <small><?php echo formatDate($exam['end_time']); ?></small>
                                    </div>
                                </div>
                                
                                <?php if ($exam['attempt_count'] > 0): ?>
                                    <div class="alert alert-warning py-2">
                                        <small><i class="fas fa-exclamation-triangle me-1"></i>Already attempted</small>
                                    </div>
                                <?php else: ?>
                                    <a href="?id=<?php echo $exam['id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-play me-1"></i>View Details
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<script>
function startExam(examId) {
    if (confirm('Are you sure you want to start this exam? The timer will begin immediately and cannot be paused.')) {
        // Create exam attempt and redirect to exam interface
        window.location.href = 'exam_interface.php?exam_id=' + examId;
    }
}

// Prevent accidental page refresh/navigation
window.addEventListener('beforeunload', function(e) {
    if (window.location.search.includes('id=')) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>

<?php include '../../templates/footer.php'; ?>
