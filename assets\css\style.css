/* WBEMS Custom Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Sidebar */
.sidebar {
    min-height: calc(100vh - 56px);
    background-color: #ffffff;
    border-right: 1px solid #dee2e6;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #495057;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: #e9ecef;
    color: var(--primary-color);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.3);
}

/* Main Content */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - 56px);
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: scale(1.05);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* Exam Cards */
.exam-card {
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.exam-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.exam-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-active {
    background-color: var(--success-color);
    color: white;
}

.status-draft {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.status-completed {
    background-color: var(--secondary-color);
    color: white;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Login Page */
.login-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-body {
    padding: 2rem;
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem;
    font-weight: 600;
}

/* Question Types */
.question-type-badge {
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.type-multiple-choice {
    background-color: #e3f2fd;
    color: #1976d2;
}

.type-true-false {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.type-short-answer {
    background-color: #e8f5e8;
    color: #388e3c;
}

.type-essay {
    background-color: #fff3e0;
    color: #f57c00;
}

/* Progress Bars */
.progress {
    height: 1rem;
    border-radius: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .main-content {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* Print Styles */
@media print {
    .sidebar, .navbar, .btn, .alert {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        margin: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}
