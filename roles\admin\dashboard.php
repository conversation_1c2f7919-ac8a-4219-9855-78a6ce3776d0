<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('admin');

$pageTitle = 'Admin Dashboard';

// Get statistics
$pdo = getDBConnection();

// Count users by role
$userStats = [];
$roles = ['admin', 'teacher', 'student', 'department_head', 'registrar'];
foreach ($roles as $role) {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role = ? AND is_active = 1");
    $stmt->execute([$role]);
    $userStats[$role] = $stmt->fetch()['count'];
}

// Get total exams
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exams");
$stmt->execute();
$totalExams = $stmt->fetch()['count'];

// Get active exams
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exams WHERE status = 'active'");
$stmt->execute();
$activeExams = $stmt->fetch()['count'];

// Get total departments
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM departments WHERE is_active = 1");
$stmt->execute();
$totalDepartments = $stmt->fetch()['count'];

// Get recent activity logs
$recentLogs = getActivityLogs(10);

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</h2>
    <div>
        <span class="text-muted">Welcome back, <?php echo $_SESSION['first_name']; ?>!</span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo array_sum($userStats); ?></h3>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $totalExams; ?></h3>
                <p class="mb-0">Total Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-play-circle fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $activeExams; ?></h3>
                <p class="mb-0">Active Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $totalDepartments; ?></h3>
                <p class="mb-0">Departments</p>
            </div>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users me-2"></i>Users by Role</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($userStats as $role => $count): ?>
                    <div class="col-6 mb-3">
                        <div class="d-flex justify-content-between">
                            <span><?php echo ucfirst(str_replace('_', ' ', $role)); ?>:</span>
                            <strong><?php echo $count; ?></strong>
                        </div>
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar" style="width: <?php echo ($count / max(array_sum($userStats), 1)) * 100; ?>%"></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="manage_users.php" class="btn btn-primary">
                        <i class="fas fa-users me-2"></i>Manage Users
                    </a>
                    <a href="view_logs.php" class="btn btn-info">
                        <i class="fas fa-list me-2"></i>View Activity Logs
                    </a>
                    <a href="manage_departments.php" class="btn btn-success">
                        <i class="fas fa-building me-2"></i>Manage Departments
                    </a>
                    <a href="system_settings.php" class="btn btn-secondary">
                        <i class="fas fa-cog me-2"></i>System Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
                <a href="view_logs.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentLogs)): ?>
                    <p class="text-muted text-center">No recent activity found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Description</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentLogs as $log): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($log['first_name'] . ' ' . $log['last_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($log['username']); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($log['action']); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['description']); ?></td>
                                    <td>
                                        <small><?php echo formatDate($log['created_at']); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../../templates/footer.php'; ?>
