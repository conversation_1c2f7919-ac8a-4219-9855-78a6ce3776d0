<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('admin');

$pageTitle = 'View Activity Logs';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get total count for pagination
$pdo = getDBConnection();
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM activity_logs");
$stmt->execute();
$totalRecords = $stmt->fetch()['total'];
$totalPages = ceil($totalRecords / $limit);

// Get activity logs with pagination
$logs = getActivityLogs($limit, $offset);

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-list me-2"></i>Activity Logs</h2>
    <div>
        <button type="button" class="btn btn-outline-danger" onclick="clearLogs()">
            <i class="fas fa-trash me-2"></i>Clear Old Logs
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="action" class="form-label">Action</label>
                <select class="form-select" name="action" id="action">
                    <option value="">All Actions</option>
                    <option value="login" <?php echo (isset($_GET['action']) && $_GET['action'] == 'login') ? 'selected' : ''; ?>>Login</option>
                    <option value="logout" <?php echo (isset($_GET['action']) && $_GET['action'] == 'logout') ? 'selected' : ''; ?>>Logout</option>
                    <option value="create_user" <?php echo (isset($_GET['action']) && $_GET['action'] == 'create_user') ? 'selected' : ''; ?>>Create User</option>
                    <option value="update_user" <?php echo (isset($_GET['action']) && $_GET['action'] == 'update_user') ? 'selected' : ''; ?>>Update User</option>
                    <option value="delete_user" <?php echo (isset($_GET['action']) && $_GET['action'] == 'delete_user') ? 'selected' : ''; ?>>Delete User</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">Date From</label>
                <input type="date" class="form-control" name="date_from" id="date_from" 
                       value="<?php echo isset($_GET['date_from']) ? $_GET['date_from'] : ''; ?>">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">Date To</label>
                <input type="date" class="form-control" name="date_to" id="date_to" 
                       value="<?php echo isset($_GET['date_to']) ? $_GET['date_to'] : ''; ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Activity Logs Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5><i class="fas fa-table me-2"></i>Activity Logs</h5>
        <span class="badge bg-primary"><?php echo $totalRecords; ?> total records</span>
    </div>
    <div class="card-body">
        <?php if (empty($logs)): ?>
            <div class="text-center py-4">
                <i class="fas fa-list fa-3x text-muted mb-3"></i>
                <p class="text-muted">No activity logs found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Action</th>
                            <th>Description</th>
                            <th>IP Address</th>
                            <th>User Agent</th>
                            <th>Date/Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                        <tr>
                            <td><?php echo $log['id']; ?></td>
                            <td>
                                <strong><?php echo htmlspecialchars($log['first_name'] . ' ' . $log['last_name']); ?></strong>
                                <br><small class="text-muted"><?php echo htmlspecialchars($log['username']); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo getActionBadgeColor($log['action']); ?>">
                                    <?php echo htmlspecialchars($log['action']); ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($log['description']); ?></td>
                            <td>
                                <small class="text-muted"><?php echo htmlspecialchars($log['ip_address']); ?></small>
                            </td>
                            <td>
                                <small class="text-muted" title="<?php echo htmlspecialchars($log['user_agent']); ?>">
                                    <?php echo substr(htmlspecialchars($log['user_agent']), 0, 30) . '...'; ?>
                                </small>
                            </td>
                            <td>
                                <small><?php echo formatDate($log['created_at']); ?></small>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="Activity logs pagination">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<script>
function clearLogs() {
    if (confirm('Are you sure you want to clear old activity logs? This action cannot be undone.')) {
        // You can implement AJAX call to clear logs older than X days
        showAlert('Feature coming soon: Clear logs older than 30 days', 'info');
    }
}

function getActionBadgeColor(action) {
    switch(action) {
        case 'login': return 'success';
        case 'logout': return 'secondary';
        case 'create_user': return 'primary';
        case 'update_user': return 'info';
        case 'delete_user': return 'danger';
        default: return 'dark';
    }
}
</script>

<?php
function getActionBadgeColor($action) {
    switch($action) {
        case 'login': return 'success';
        case 'logout': return 'secondary';
        case 'create_user': return 'primary';
        case 'update_user': return 'info';
        case 'delete_user': return 'danger';
        default: return 'dark';
    }
}

include '../../templates/footer.php';
?>
