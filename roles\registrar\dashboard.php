<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('registrar');

$pageTitle = 'Registrar Dashboard';

// Get system-wide statistics
$pdo = getDBConnection();

// Get overall statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE is_active = 1");
$stmt->execute();
$totalUsers = $stmt->fetch()['count'];

$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exams");
$stmt->execute();
$totalExams = $stmt->fetch()['count'];

$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exam_attempts WHERE status = 'completed'");
$stmt->execute();
$completedAttempts = $stmt->fetch()['count'];

$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM departments WHERE is_active = 1");
$stmt->execute();
$totalDepartments = $stmt->fetch()['count'];

// Get exam statistics by department
$stmt = $pdo->prepare("SELECT d.name as department_name, COUNT(e.id) as exam_count 
                      FROM departments d 
                      LEFT JOIN subjects s ON d.id = s.department_id 
                      LEFT JOIN exams e ON s.id = e.subject_id 
                      WHERE d.is_active = 1 
                      GROUP BY d.id, d.name 
                      ORDER BY exam_count DESC");
$stmt->execute();
$departmentStats = $stmt->fetchAll();

// Get recent system activity
$recentLogs = getActivityLogs(15);

// Get exam performance overview
$stmt = $pdo->prepare("SELECT 
                        AVG(percentage) as avg_score,
                        COUNT(*) as total_attempts,
                        SUM(CASE WHEN percentage >= 90 THEN 1 ELSE 0 END) as grade_a,
                        SUM(CASE WHEN percentage >= 80 AND percentage < 90 THEN 1 ELSE 0 END) as grade_b,
                        SUM(CASE WHEN percentage >= 70 AND percentage < 80 THEN 1 ELSE 0 END) as grade_c,
                        SUM(CASE WHEN percentage >= 60 AND percentage < 70 THEN 1 ELSE 0 END) as grade_d,
                        SUM(CASE WHEN percentage < 60 THEN 1 ELSE 0 END) as grade_f
                      FROM exam_attempts 
                      WHERE status = 'completed'");
$stmt->execute();
$performanceStats = $stmt->fetch();

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-pie me-2"></i>Registrar Dashboard</h2>
    <div>
        <span class="text-muted">System Overview | Welcome, <?php echo $_SESSION['first_name']; ?>!</span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $totalUsers; ?></h3>
                <p class="mb-0">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $totalExams; ?></h3>
                <p class="mb-0">Total Exams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $completedAttempts; ?></h3>
                <p class="mb-0">Completed Attempts</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $totalDepartments; ?></h3>
                <p class="mb-0">Departments</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Performance Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-rocket me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="generate_reports.php" class="btn btn-primary">
                        <i class="fas fa-file-alt me-2"></i>Generate Reports
                    </a>
                    <a href="system_reports.php" class="btn btn-info">
                        <i class="fas fa-chart-pie me-2"></i>System Reports
                    </a>
                    <a href="export_data.php" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Export Data
                    </a>
                    <a href="analytics.php" class="btn btn-secondary">
                        <i class="fas fa-analytics me-2"></i>Analytics Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar me-2"></i>Performance Overview</h5>
            </div>
            <div class="card-body">
                <?php if ($performanceStats && $performanceStats['total_attempts'] > 0): ?>
                    <div class="mb-3">
                        <h6>Average Score: <span class="text-primary"><?php echo number_format($performanceStats['avg_score'], 1); ?>%</span></h6>
                    </div>
                    <div class="row text-center">
                        <div class="col">
                            <div class="text-success">
                                <strong><?php echo $performanceStats['grade_a']; ?></strong>
                                <br><small>A (90%+)</small>
                            </div>
                        </div>
                        <div class="col">
                            <div class="text-info">
                                <strong><?php echo $performanceStats['grade_b']; ?></strong>
                                <br><small>B (80-89%)</small>
                            </div>
                        </div>
                        <div class="col">
                            <div class="text-warning">
                                <strong><?php echo $performanceStats['grade_c']; ?></strong>
                                <br><small>C (70-79%)</small>
                            </div>
                        </div>
                        <div class="col">
                            <div class="text-secondary">
                                <strong><?php echo $performanceStats['grade_d']; ?></strong>
                                <br><small>D (60-69%)</small>
                            </div>
                        </div>
                        <div class="col">
                            <div class="text-danger">
                                <strong><?php echo $performanceStats['grade_f']; ?></strong>
                                <br><small>F (<60%)</small>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">No exam data available yet.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Department Statistics -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-building me-2"></i>Department Statistics</h5>
                <a href="department_analysis.php" class="btn btn-sm btn-outline-primary">Detailed Analysis</a>
            </div>
            <div class="card-body">
                <?php if (empty($departmentStats)): ?>
                    <p class="text-muted text-center">No department data available.</p>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($departmentStats as $dept): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6 class="card-title"><?php echo htmlspecialchars($dept['department_name']); ?></h6>
                                    <h4 class="text-primary"><?php echo $dept['exam_count']; ?></h4>
                                    <small class="text-muted">Total Exams</small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent System Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-history me-2"></i>Recent System Activity</h5>
                <a href="system_logs.php" class="btn btn-sm btn-outline-primary">View All Logs</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentLogs)): ?>
                    <p class="text-muted text-center">No recent activity found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Description</th>
                                    <th>IP Address</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentLogs as $log): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($log['first_name'] . ' ' . $log['last_name']); ?></strong>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($log['username']); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($log['action']); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($log['description']); ?></td>
                                    <td>
                                        <small class="text-muted"><?php echo htmlspecialchars($log['ip_address']); ?></small>
                                    </td>
                                    <td>
                                        <small><?php echo formatDate($log['created_at']); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../../templates/footer.php'; ?>
