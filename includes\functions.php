<?php
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../config/constants.php';

// Utility functions

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Format date for display
function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
    return date($format, strtotime($date));
}

// Generate random string
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

// Get user by ID
function getUserById($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    } catch (Exception $e) {
        return false;
    }
}

// Get all users with optional role filter
function getUsers($role = null, $limit = null, $offset = 0) {
    try {
        $pdo = getDBConnection();
        $sql = "SELECT u.*, d.name as department_name FROM users u 
                LEFT JOIN departments d ON u.department_id = d.id 
                WHERE u.is_active = 1";
        $params = [];
        
        if ($role) {
            $sql .= " AND u.role = ?";
            $params[] = $role;
        }
        
        $sql .= " ORDER BY u.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// Get departments
function getDepartments() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM departments WHERE is_active = 1 ORDER BY name");
        $stmt->execute();
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// Get subjects by department
function getSubjectsByDepartment($departmentId) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT s.*, u.first_name, u.last_name FROM subjects s 
                              LEFT JOIN users u ON s.teacher_id = u.id 
                              WHERE s.department_id = ? AND s.is_active = 1 
                              ORDER BY s.name");
        $stmt->execute([$departmentId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// Get exams by teacher
function getExamsByTeacher($teacherId) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT e.*, s.name as subject_name FROM exams e 
                              JOIN subjects s ON e.subject_id = s.id 
                              WHERE e.teacher_id = ? 
                              ORDER BY e.created_at DESC");
        $stmt->execute([$teacherId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// Get available exams for student
function getAvailableExamsForStudent($studentId) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT e.*, s.name as subject_name, 
                              (SELECT COUNT(*) FROM exam_attempts ea WHERE ea.exam_id = e.id AND ea.student_id = ?) as attempt_count
                              FROM exams e 
                              JOIN subjects s ON e.subject_id = s.id 
                              JOIN enrollments en ON s.id = en.subject_id 
                              WHERE en.student_id = ? AND e.status = 'active' 
                              AND e.start_time <= NOW() AND e.end_time >= NOW()
                              ORDER BY e.start_time ASC");
        $stmt->execute([$studentId, $studentId]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// Create user
function createUser($data) {
    try {
        $pdo = getDBConnection();
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, first_name, last_name, role, department_id, student_id, employee_id, phone, address) 
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        return $stmt->execute([
            $data['username'],
            $data['email'],
            $hashedPassword,
            $data['first_name'],
            $data['last_name'],
            $data['role'],
            $data['department_id'] ?? null,
            $data['student_id'] ?? null,
            $data['employee_id'] ?? null,
            $data['phone'] ?? null,
            $data['address'] ?? null
        ]);
    } catch (Exception $e) {
        return false;
    }
}

// Update user
function updateUser($id, $data) {
    try {
        $pdo = getDBConnection();
        $sql = "UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, role = ?, department_id = ?, student_id = ?, employee_id = ?, phone = ?, address = ?";
        $params = [
            $data['username'],
            $data['email'],
            $data['first_name'],
            $data['last_name'],
            $data['role'],
            $data['department_id'] ?? null,
            $data['student_id'] ?? null,
            $data['employee_id'] ?? null,
            $data['phone'] ?? null,
            $data['address'] ?? null
        ];
        
        if (!empty($data['password'])) {
            $sql .= ", password = ?";
            $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $id;
        
        $stmt = $pdo->prepare($sql);
        return $stmt->execute($params);
    } catch (Exception $e) {
        return false;
    }
}

// Delete user (soft delete)
function deleteUser($id) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("UPDATE users SET is_active = 0 WHERE id = ?");
        return $stmt->execute([$id]);
    } catch (Exception $e) {
        return false;
    }
}

// Get activity logs
function getActivityLogs($limit = 50, $offset = 0) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT al.*, u.username, u.first_name, u.last_name 
                              FROM activity_logs al 
                              JOIN users u ON al.user_id = u.id 
                              ORDER BY al.created_at DESC 
                              LIMIT ? OFFSET ?");
        $stmt->execute([$limit, $offset]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// Calculate grade letter
function calculateGradeLetter($percentage) {
    if ($percentage >= GRADE_A) return 'A';
    if ($percentage >= GRADE_B) return 'B';
    if ($percentage >= GRADE_C) return 'C';
    if ($percentage >= GRADE_D) return 'D';
    return 'F';
}

// Check if exam is active
function isExamActive($exam) {
    $now = date('Y-m-d H:i:s');
    return $exam['status'] === 'active' && 
           $exam['start_time'] <= $now && 
           $exam['end_time'] >= $now;
}
?>
