<?php
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

checkRole('department_head');

$pageTitle = 'Department Head Dashboard';

// Get department head's statistics
$pdo = getDBConnection();
$deptHeadId = $_SESSION['user_id'];
$departmentId = $_SESSION['department_id'];

// Get department information
$stmt = $pdo->prepare("SELECT * FROM departments WHERE id = ?");
$stmt->execute([$departmentId]);
$department = $stmt->fetch();

// Get department statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE department_id = ? AND role = 'teacher' AND is_active = 1");
$stmt->execute([$departmentId]);
$teacherCount = $stmt->fetch()['count'];

$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE department_id = ? AND role = 'student' AND is_active = 1");
$stmt->execute([$departmentId]);
$studentCount = $stmt->fetch()['count'];

$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM subjects WHERE department_id = ? AND is_active = 1");
$stmt->execute([$departmentId]);
$subjectCount = $stmt->fetch()['count'];

// Get exams in department
$stmt = $pdo->prepare("SELECT COUNT(*) as count FROM exams e 
                      JOIN subjects s ON e.subject_id = s.id 
                      WHERE s.department_id = ?");
$stmt->execute([$departmentId]);
$examCount = $stmt->fetch()['count'];

// Get recent exams in department
$stmt = $pdo->prepare("SELECT e.*, s.name as subject_name, u.first_name, u.last_name 
                      FROM exams e 
                      JOIN subjects s ON e.subject_id = s.id 
                      JOIN users u ON e.teacher_id = u.id 
                      WHERE s.department_id = ? 
                      ORDER BY e.created_at DESC 
                      LIMIT 10");
$stmt->execute([$departmentId]);
$recentExams = $stmt->fetchAll();

// Get department teachers
$stmt = $pdo->prepare("SELECT u.*, COUNT(e.id) as exam_count 
                      FROM users u 
                      LEFT JOIN exams e ON u.id = e.teacher_id 
                      WHERE u.department_id = ? AND u.role = 'teacher' AND u.is_active = 1 
                      GROUP BY u.id");
$stmt->execute([$departmentId]);
$teachers = $stmt->fetchAll();

include '../../templates/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-building me-2"></i>Department Head Dashboard</h2>
    <div>
        <span class="text-muted">
            <?php echo htmlspecialchars($department['name'] ?? 'Department'); ?> | 
            Welcome, <?php echo $_SESSION['first_name']; ?>!
        </span>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $teacherCount; ?></h3>
                <p class="mb-0">Teachers</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-user-graduate fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $studentCount; ?></h3>
                <p class="mb-0">Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-book fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $subjectCount; ?></h3>
                <p class="mb-0">Subjects</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                <h3 class="stats-number"><?php echo $examCount; ?></h3>
                <p class="mb-0">Total Exams</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Department Info -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-rocket me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="monitor_exams.php" class="btn btn-primary">
                        <i class="fas fa-monitor me-2"></i>Monitor Exams
                    </a>
                    <a href="department_reports.php" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i>Department Reports
                    </a>
                    <a href="teacher_activities.php" class="btn btn-success">
                        <i class="fas fa-user-tie me-2"></i>Teacher Activities
                    </a>
                    <a href="student_performance.php" class="btn btn-secondary">
                        <i class="fas fa-chart-line me-2"></i>Student Performance
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>Department Information</h5>
            </div>
            <div class="card-body">
                <?php if ($department): ?>
                    <h6><?php echo htmlspecialchars($department['name']); ?></h6>
                    <p class="text-muted mb-2">
                        <strong>Code:</strong> <?php echo htmlspecialchars($department['code']); ?>
                    </p>
                    <?php if ($department['description']): ?>
                        <p class="text-muted mb-2">
                            <?php echo htmlspecialchars($department['description']); ?>
                        </p>
                    <?php endif; ?>
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        Established: <?php echo formatDate($department['created_at']); ?>
                    </small>
                <?php else: ?>
                    <p class="text-muted">Department information not available.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Teachers Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-chalkboard-teacher me-2"></i>Department Teachers</h5>
                <a href="teacher_activities.php" class="btn btn-sm btn-outline-primary">View Details</a>
            </div>
            <div class="card-body">
                <?php if (empty($teachers)): ?>
                    <p class="text-muted text-center">No teachers assigned to this department yet.</p>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($teachers as $teacher): ?>
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?>
                                    </h6>
                                    <p class="card-text">
                                        <small class="text-muted"><?php echo htmlspecialchars($teacher['email']); ?></small>
                                        <?php if ($teacher['employee_id']): ?>
                                            <br><small class="text-muted">ID: <?php echo htmlspecialchars($teacher['employee_id']); ?></small>
                                        <?php endif; ?>
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-primary"><?php echo $teacher['exam_count']; ?> exams</span>
                                        <small class="text-muted"><?php echo formatDate($teacher['created_at']); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Exams -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-clipboard-list me-2"></i>Recent Exams</h5>
                <a href="monitor_exams.php" class="btn btn-sm btn-outline-primary">Monitor All</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentExams)): ?>
                    <p class="text-muted text-center">No exams created yet in this department.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Exam Title</th>
                                    <th>Subject</th>
                                    <th>Teacher</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentExams as $exam): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($exam['title']); ?></strong>
                                        <br><small class="text-muted"><?php echo ucfirst($exam['exam_type']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($exam['subject_name']); ?></td>
                                    <td><?php echo htmlspecialchars($exam['first_name'] . ' ' . $exam['last_name']); ?></td>
                                    <td>
                                        <span class="exam-status status-<?php echo $exam['status']; ?>">
                                            <?php echo ucfirst($exam['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo formatDate($exam['start_time']); ?></small>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../../templates/footer.php'; ?>
