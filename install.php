<?php
require_once 'config/db.php';
require_once 'config/constants.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Initialize database connection
        $pdo = initializeDatabase();
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        
        // Split by semicolon and execute each statement
        $statements = explode(';', $schema);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Skip if table already exists
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        $message = 'Database installed successfully! You can now login with:<br>
                   <strong>Admin:</strong> username: admin, password: password<br>
                   <strong>Teacher:</strong> username: teacher1, password: password<br>
                   <strong>Student:</strong> username: student1, password: password';
    } catch (Exception $e) {
        $error = 'Error installing database: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WBEMS Installation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .install-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-body {
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="install-card">
                    <div class="install-header">
                        <h3><i class="fas fa-graduation-cap me-2"></i>WBEMS Installation</h3>
                        <p class="mb-0">Web-Based Exam Management System</p>
                    </div>
                    <div class="install-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                                <hr>
                                <a href="login.php" class="btn btn-success">
                                    <i class="fas fa-sign-in-alt me-2"></i>Go to Login
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$message): ?>
                            <div class="mb-4">
                                <h5>Installation Requirements:</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <i class="fas fa-check text-success me-2"></i>XAMPP with Apache and MySQL running
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check text-success me-2"></i>PHP 7.4 or higher
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check text-success me-2"></i>MySQL 5.7 or higher
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check text-success me-2"></i>PDO MySQL extension enabled
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="mb-4">
                                <h5>What will be installed:</h5>
                                <ul class="list-group">
                                    <li class="list-group-item">
                                        <i class="fas fa-database text-primary me-2"></i>Complete database structure
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-users text-primary me-2"></i>Role-based user system
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-graduation-cap text-primary me-2"></i>Exam management system
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-chart-bar text-primary me-2"></i>Reporting and analytics
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="mb-4">
                                <h5>Database Configuration:</h5>
                                <p>Current settings (modify in <code>config/db.php</code> if needed):</p>
                                <ul>
                                    <li><strong>Host:</strong> <?php echo DB_HOST; ?></li>
                                    <li><strong>Database:</strong> <?php echo DB_NAME; ?></li>
                                    <li><strong>User:</strong> <?php echo DB_USER; ?></li>
                                </ul>
                            </div>
                            
                            <form method="POST" action="">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-cogs me-2"></i>Install WBEMS
                                    </button>
                                </div>
                            </form>
                            
                            <div class="text-center mt-4">
                                <small class="text-muted">
                                    This will create the database and install sample data.
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
